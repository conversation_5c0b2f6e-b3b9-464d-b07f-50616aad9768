{"name": "erc20_usdt", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"addRepeatJob": "node -r dotenv/config ./src/workers/addRepeatJob.js dotenv_config_path=./.env dotenv_config_debug=true", "incrNum": "node -r dotenv/config ./src/workers/incrNum.js dotenv_config_path=./.env dotenv_config_debug=true", "getLogs": "node -r dotenv/config ./src/workers/getLogs.js dotenv_config_path=./.env dotenv_config_debug=true", "checkLogs_erc20_usdt_data": "node -r dotenv/config ./src/workers/checkLogs_erc20_usdt_data.js dotenv_config_path=./.env dotenv_config_debug=true", "updateTokenBalance": "node -r dotenv/config ./src/workers/updateTokenBalance.js dotenv_config_path=./.env dotenv_config_debug=true", "ui": "node -r dotenv/config ./src/ui.js dotenv_config_path=./.env dotenv_config_debug=true", "start": "concurrently -n addRepeatJob,incrNum,getLogs,checkLogs_erc20_usdt_data,updateTokenBalance,ui -c auto npm:addRepeatJob npm:incrNum npm:getLogs npm:checkLogs_erc20_usdt_data npm:updateTokenBalance npm:ui"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@bull-board/express": "^4.11.1", "@taskforcesh/bullmq-pro": "^5.1.14", "body-parser": "^1.20.1", "concurrently": "^7.6.0", "connect-ensure-login": "^0.1.1", "dotenv": "^16.0.3", "ethers": "^5.7.2", "express-session": "^1.17.3", "knex": "^2.4.2", "moment": "^2.29.4", "mysql": "^2.18.1", "mysql2": "^3.1.2", "passport": "^0.6.0", "passport-local": "^1.0.0"}}