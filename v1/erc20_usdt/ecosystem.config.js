const {
  calcPath,
  getEnvVariables
} = require('./helpers');

module.exports = {
  apps: [

    {
      name: "v1/erc20-usdt/getLogs",
      cwd: calcPath('src/workers'),
      script: "getLogs.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-usdt/addRepeatJob",
      cwd: calcPath('src/workers'),
      script: "addRepeatJob.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-usdt/checkLogs_erc20_usdt_data",
      cwd: calcPath('src/workers'),
      script: "checkLogs_erc20_usdt_data.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-usdt/incrNum",
      cwd: calcPath('src/workers'),
      script: "incrNum.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-usdt/updateTokenBalance",
      cwd: calcPath('src/workers'),
      script: "updateTokenBalance.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/erc20-usdt/ui",
      cwd: calcPath('src'),
      script: "ui.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    // {
    //   name: "tmp/getaddress",
    //   cwd: calcPath('src/workers'),
    //   script: "getaddress.js",
    //   exec_mode: "fork",
    //   kill_timeout: 60000,
    //   args: [],
    //   env: getEnvVariables()
    // },

    // {
    //   name: "tmp/addAddressRep",
    //   cwd: calcPath('src/workers'),
    //   script: "addAddressRep.js",
    //   exec_mode: "fork",
    //   kill_timeout: 60000,
    //   args: [],
    //   env: getEnvVariables()
    // },
    // {
    //   name: "tmp/tmpUpdateBalance",
    //   cwd: calcPath('src/workers'),
    //   script: "tmpUpdateBalance.js",
    //   exec_mode: "fork",
    //   kill_timeout: 60000,
    //   args: [],
    //   env: getEnvVariables()
    // },

  ]
}