const {
    createBullBoard
  } = require('@bull-board/api');
  const {
    BullMQAdapter
  } = require('@bull-board/api/bullMQAdapter');
  const {
    ExpressAdapter
  } = require('@bull-board/express');
  const {
    Queue: QueueMQ,
    Worker,
    QueueScheduler
  } = require('bullmq');
  const session = require('express-session');
  const bodyParser = require('body-parser');
  const passport = require('passport');
  const LocalStrategy = require('passport-local').Strategy;
  const {
    ensureLoggedIn
  } = require('connect-ensure-login');
  const express = require('express');
  const { createLogger } = require('./utils/logger');
  const { globalMonitor } = require('./utils/performanceMonitor');

  const logger = createLogger('ui');
  
  // Configure the local strategy for use by Passport. 
  //
  // The local strategy require a `verify` function which receives the credentials
  // (`username` and `password`) submitted by the user.  The function must verify
  // that the password is correct and then invoke `cb` with a user object, which
  // will be set at `req.user` in route handlers after authentication.
  passport.use(
    new LocalStrategy(function (username, password, cb) {
      if (username === process.env.ui_username && password === process.env.ui_password) {
        return cb(null, {
          user: 'erc20-usdt-board'
        });
      }
      return cb(null, false);
    })
  );
  
  // Configure Passport authenticated session persistence.
  //
  // In order to restore authentication state across HTTP requests, Passport needs
  // to serialize users into and deserialize users out of the session.  The
  // typical implementation of this is as simple as supplying the user ID when
  // serializing, and querying the user record by ID from the database when
  // deserializing.
  passport.serializeUser((user, cb) => {
    cb(null, user);
  });
  
  passport.deserializeUser((user, cb) => {
    cb(null, user);
  });
  
  const sleep = (t) => new Promise((resolve) => setTimeout(resolve, t * 1000));
  
  
  const redisConfig10 = require('./config/redis_config')(10);

  
  const redisConfig19 = require('./config/redis_config')(19);
  
  const run = async () => {
  
    const serverAdapter = new ExpressAdapter();
    serverAdapter.setBasePath('/ui');
  
  
    const {
      addQueue,
      removeQueue,
      setQueues,
      replaceQueues
    } = createBullBoard({
      queues: [
  
  
        new BullMQAdapter(new QueueMQ('erc20-usdt-block-track', {
          connection: redisConfig10
        })),
  
        new BullMQAdapter(new QueueMQ('erc20-usdt-queryFilter', {
          connection: redisConfig10
        })),
  
        new BullMQAdapter(new QueueMQ('checkLogs-erc20-usdt', {
          connection: redisConfig10
        })),
  
        new BullMQAdapter(new QueueMQ('updateerc20UsdtBalanceQueuePro', {
          connection: redisConfig10
        })),

        // new BullMQAdapter(new QueueMQ('addr-track', {
        //   connection: redisConfig19
        // })),
        // new BullMQAdapter(new QueueMQ('updateerc20UsdtBalanceQueueProTmp', {
        //   connection: redisConfig19
        // })),
    
  

        
      ],
      serverAdapter: serverAdapter,
    });
  
    //   await setupBullMQProcessor(exampleBullMq.name);
  
    const app = express();

    // 添加性能监控中间件
    app.use(globalMonitor.createExpressMiddleware());

    // Configure view engine to render EJS templates.
    app.set('views', __dirname + '/views');
    app.set('view engine', 'ejs');
  
    app.use(session({
      secret: process.env.ui_session_secret || 'default-secret-key',
      saveUninitialized: true,
      resave: true
    }));
    app.use(bodyParser.urlencoded({
      extended: false
    }));
  
    // Initialize Passport and restore authentication state, if any, from the session.
    app.use(passport.initialize({}));
    app.use(passport.session({}));
  
    app.get('/ui/login', (req, res) => {
      res.render('login', {
        invalid: req.query.invalid === 'true'
      });
    });
  
    app.post(
      '/ui/login',
      passport.authenticate('local', {
        failureRedirect: '/ui/login?invalid=true'
      }),
      (req, res) => {
        res.redirect('/ui');
      }
    );
  
  
    // 健康检查端点
    app.get('/health', (req, res) => {
      const healthCheck = {
        uptime: process.uptime(),
        message: 'OK',
        timestamp: Date.now(),
        service: 'erc20-usdt-monitor',
        version: require('../package.json').version || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      };

      try {
        res.status(200).json(healthCheck);
        logger.debug('Health check requested', { ip: req.ip });
      } catch (error) {
        healthCheck.message = 'ERROR';
        res.status(503).json(healthCheck);
        logger.error('Health check failed', error);
      }
    });

    // 详细状态检查端点
    app.get('/status', (req, res) => {
      const performanceStats = globalMonitor.getStats();
      const status = {
        service: 'erc20-usdt-monitor',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: require('../package.json').version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        queues: {
          'erc20-usdt-queryFilter': 'active',
          'erc20-usdt-balance-update': 'active',
          'checkLogs-erc20-usdt': 'active'
        },
        performance: performanceStats
      };

      res.status(200).json(status);
      logger.debug('Status check requested', { ip: req.ip });
    });

    // 性能监控端点
    app.get('/metrics', (req, res) => {
      const stats = globalMonitor.getStats();
      res.status(200).json({
        timestamp: new Date().toISOString(),
        service: 'erc20-usdt-monitor',
        ...stats
      });
      logger.debug('Metrics requested', { ip: req.ip });
    });

    app.use('/ui', ensureLoggedIn({
      redirectTo: '/ui/login'
    }), serverAdapter.getRouter());

    app.listen(8118, () => {
      logger.info('UI server started on port 8118');
      logger.info('Dashboard available at http://localhost:8118/ui');
      logger.info('Health check available at http://localhost:8118/health');
      logger.info('Status check available at http://localhost:8118/status');
      logger.info('Performance metrics available at http://localhost:8118/metrics');
    });
  };
  
  // eslint-disable-next-line no-console
  run().catch((e) => console.error(e));