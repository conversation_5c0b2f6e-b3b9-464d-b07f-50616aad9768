/**
 * 性能监控工具
 * 收集和报告系统性能指标
 */

const { createLogger } = require('./logger');
const logger = createLogger('performance');

class PerformanceMonitor {
    constructor(options = {}) {
        this.options = {
            reportInterval: options.reportInterval || 60000, // 1分钟
            memoryThreshold: options.memoryThreshold || 500 * 1024 * 1024, // 500MB
            cpuThreshold: options.cpuThreshold || 80, // 80%
            ...options
        };
        
        this.metrics = {
            startTime: Date.now(),
            requests: 0,
            errors: 0,
            jobsProcessed: 0,
            jobsFailed: 0,
            lastReport: Date.now()
        };
        
        this.timers = new Map();
        this.intervals = new Map();
        
        this.startMonitoring();
    }
    
    /**
     * 开始监控
     */
    startMonitoring() {
        // 定期报告性能指标
        const reportInterval = setInterval(() => {
            this.reportMetrics();
        }, this.options.reportInterval);
        
        this.intervals.set('report', reportInterval);
        
        // 监控内存使用
        const memoryInterval = setInterval(() => {
            this.checkMemoryUsage();
        }, 30000); // 30秒检查一次
        
        this.intervals.set('memory', memoryInterval);
        
        logger.info('Performance monitoring started', {
            reportInterval: this.options.reportInterval,
            memoryThreshold: this.options.memoryThreshold
        });
    }
    
    /**
     * 停止监控
     */
    stopMonitoring() {
        this.intervals.forEach((interval, name) => {
            clearInterval(interval);
            logger.debug(`Stopped ${name} monitoring interval`);
        });
        
        this.intervals.clear();
        logger.info('Performance monitoring stopped');
    }
    
    /**
     * 记录请求
     */
    recordRequest() {
        this.metrics.requests++;
    }
    
    /**
     * 记录错误
     */
    recordError() {
        this.metrics.errors++;
    }
    
    /**
     * 记录作业处理
     */
    recordJobProcessed() {
        this.metrics.jobsProcessed++;
    }
    
    /**
     * 记录作业失败
     */
    recordJobFailed() {
        this.metrics.jobsFailed++;
    }
    
    /**
     * 开始计时
     */
    startTimer(name) {
        this.timers.set(name, {
            start: process.hrtime.bigint(),
            name
        });
    }
    
    /**
     * 结束计时并记录
     */
    endTimer(name) {
        const timer = this.timers.get(name);
        if (!timer) {
            logger.warn(`Timer ${name} not found`);
            return null;
        }
        
        const end = process.hrtime.bigint();
        const duration = Number(end - timer.start) / 1000000; // 转换为毫秒
        
        this.timers.delete(name);
        
        logger.debug(`Timer ${name} completed`, { duration: `${duration.toFixed(2)}ms` });
        
        return duration;
    }
    
    /**
     * 获取系统内存使用情况
     */
    getMemoryUsage() {
        const usage = process.memoryUsage();
        return {
            rss: usage.rss, // 常驻集大小
            heapTotal: usage.heapTotal, // 堆总大小
            heapUsed: usage.heapUsed, // 已使用堆大小
            external: usage.external, // 外部内存使用
            arrayBuffers: usage.arrayBuffers // ArrayBuffer使用
        };
    }
    
    /**
     * 获取系统CPU使用情况
     */
    getCPUUsage() {
        const usage = process.cpuUsage();
        return {
            user: usage.user, // 用户CPU时间（微秒）
            system: usage.system // 系统CPU时间（微秒）
        };
    }
    
    /**
     * 检查内存使用情况
     */
    checkMemoryUsage() {
        const memory = this.getMemoryUsage();
        
        if (memory.heapUsed > this.options.memoryThreshold) {
            logger.warn('High memory usage detected', {
                heapUsed: `${(memory.heapUsed / 1024 / 1024).toFixed(2)}MB`,
                threshold: `${(this.options.memoryThreshold / 1024 / 1024).toFixed(2)}MB`,
                heapTotal: `${(memory.heapTotal / 1024 / 1024).toFixed(2)}MB`
            });
        }
    }
    
    /**
     * 获取运行时间
     */
    getUptime() {
        return Date.now() - this.metrics.startTime;
    }
    
    /**
     * 获取性能统计
     */
    getStats() {
        const uptime = this.getUptime();
        const memory = this.getMemoryUsage();
        const cpu = this.getCPUUsage();
        
        return {
            uptime: uptime,
            uptimeFormatted: this.formatDuration(uptime),
            memory: {
                rss: `${(memory.rss / 1024 / 1024).toFixed(2)}MB`,
                heapTotal: `${(memory.heapTotal / 1024 / 1024).toFixed(2)}MB`,
                heapUsed: `${(memory.heapUsed / 1024 / 1024).toFixed(2)}MB`,
                external: `${(memory.external / 1024 / 1024).toFixed(2)}MB`
            },
            cpu: {
                user: `${(cpu.user / 1000).toFixed(2)}ms`,
                system: `${(cpu.system / 1000).toFixed(2)}ms`
            },
            metrics: {
                requests: this.metrics.requests,
                errors: this.metrics.errors,
                jobsProcessed: this.metrics.jobsProcessed,
                jobsFailed: this.metrics.jobsFailed,
                errorRate: this.metrics.requests > 0 ? 
                    ((this.metrics.errors / this.metrics.requests) * 100).toFixed(2) + '%' : '0%',
                jobFailureRate: this.metrics.jobsProcessed > 0 ? 
                    ((this.metrics.jobsFailed / this.metrics.jobsProcessed) * 100).toFixed(2) + '%' : '0%'
            }
        };
    }
    
    /**
     * 报告性能指标
     */
    reportMetrics() {
        const stats = this.getStats();
        const timeSinceLastReport = Date.now() - this.metrics.lastReport;
        
        logger.info('Performance Report', {
            ...stats,
            reportInterval: this.formatDuration(timeSinceLastReport)
        });
        
        this.metrics.lastReport = Date.now();
    }
    
    /**
     * 格式化持续时间
     */
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) {
            return `${days}d ${hours % 24}h ${minutes % 60}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }
    
    /**
     * 创建性能中间件（用于Express）
     */
    createExpressMiddleware() {
        return (req, res, next) => {
            const start = process.hrtime.bigint();
            
            this.recordRequest();
            
            res.on('finish', () => {
                const end = process.hrtime.bigint();
                const duration = Number(end - start) / 1000000;
                
                if (res.statusCode >= 400) {
                    this.recordError();
                }
                
                logger.debug('HTTP Request', {
                    method: req.method,
                    url: req.url,
                    statusCode: res.statusCode,
                    duration: `${duration.toFixed(2)}ms`,
                    userAgent: req.get('User-Agent')
                });
            });
            
            next();
        };
    }
}

// 创建全局实例
const globalMonitor = new PerformanceMonitor();

// 优雅关闭处理
process.on('SIGTERM', () => {
    globalMonitor.stopMonitoring();
});

process.on('SIGINT', () => {
    globalMonitor.stopMonitoring();
});

module.exports = {
    PerformanceMonitor,
    globalMonitor
};
