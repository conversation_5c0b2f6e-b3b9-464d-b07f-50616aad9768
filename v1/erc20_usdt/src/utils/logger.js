/**
 * 统一日志记录器
 * 提供格式化的日志输出，便于调试和监控
 */

const moment = require('moment');

class Logger {
    constructor(moduleName) {
        this.moduleName = moduleName;
    }

    _formatMessage(level, message, data = null) {
        const timestamp = moment().format('YYYY-MM-DD HH:mm:ss');
        const prefix = `[${timestamp}] [${level}] [${this.moduleName}]`;
        
        if (data) {
            return `${prefix} ${message} ${JSON.stringify(data)}`;
        }
        return `${prefix} ${message}`;
    }

    info(message, data = null) {
        console.log(this._formatMessage('INFO', message, data));
    }

    warn(message, data = null) {
        console.warn(this._formatMessage('WARN', message, data));
    }

    error(message, error = null, data = null) {
        const errorInfo = error ? {
            message: error.message,
            stack: error.stack,
            ...data
        } : data;
        
        console.error(this._formatMessage('ERROR', message, errorInfo));
    }

    debug(message, data = null) {
        if (process.env.NODE_ENV === 'development' || process.env.DEBUG === 'true') {
            console.log(this._formatMessage('DEBUG', message, data));
        }
    }

    success(message, data = null) {
        console.log(this._formatMessage('SUCCESS', message, data));
    }

    // 队列相关的专用日志方法
    queueCompleted(queueName, jobId, result = null) {
        this.success(`Queue ${queueName} job ${jobId} completed`, result);
    }

    queueFailed(queueName, jobId, error, jobData = null) {
        this.error(`Queue ${queueName} job ${jobId} failed`, error, jobData);
    }

    queueError(queueName, error) {
        this.error(`Queue ${queueName} error`, error);
    }

    queuePaused(queueName, jobId) {
        this.warn(`Queue ${queueName} job ${jobId} paused`);
    }

    queueResumed(queueName, jobId) {
        this.info(`Queue ${queueName} job ${jobId} resumed`);
    }

    queueClosed(queueName) {
        this.warn(`Queue ${queueName} worker closed`);
    }

    // 区块链相关的专用日志方法
    blockProcessing(blockNumber, chainBlockNumber) {
        this.info(`Processing block ${blockNumber}, chain at ${chainBlockNumber}`);
    }

    transactionProcessed(txHash, from, to, amount) {
        this.info(`Transaction processed: ${txHash}`, {
            from,
            to,
            amount
        });
    }

    balanceUpdated(address, balance, table) {
        this.info(`Balance updated for ${address}: ${balance}`, { table });
    }
}

// 创建不同模块的日志记录器实例
const createLogger = (moduleName) => new Logger(moduleName);

module.exports = {
    Logger,
    createLogger
};
