const {
    ethers
} = require("ethers");

const moment = require('moment');
const { createLogger } = require('../utils/logger');
const { globalMonitor } = require('../utils/performanceMonitor');

const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const connection10 = require('../config/redis_config')(10);

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const {
    address,
    abi,
    decimals
} = require("../abi/erc20-usdt");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);


const {
    coin
} = require('../config/coin_config');

const checkLogs_erc20_usdt_Queue = new QueuePro(`checkLogs-${coin}`, {
    connection: connection10
});

const contract = new ethers.Contract(address, abi, provider);
const logger = createLogger('getLogs');

const worker = new WorkerPro(`${coin}-queryFilter`, async (job) => {

    logger.debug('Processing job', job.data);
    globalMonitor.startTimer(`queryFilter-${job.id}`);

    let starnum =  Number(job.data.dbBlockNum);

    const results = await contract.queryFilter('Transfer', starnum, starnum + 1);

    let arr = [];
    for (let i = 0; i < results.length; i++) {

        // console.log(ethers.utils.formatUnits(ethers.BigNumber.from((results[i].args['value'])), decimals), results[i].transactionHash);
        arr.push({
            name: `num:${results[i].transactionHash}`,
            data: {
                blockNumber: results[i].blockNumber,
                transactionHash: results[i].transactionHash,
                from: (results[i].args['from']).toLowerCase(),
                to: (results[i].args['to']).toLowerCase(),
                chainID: 'erc20',
                contractAddress: address,
                chain: 'erc20-usdt',
                decimals,
                value: ethers.utils.formatUnits(ethers.BigNumber.from((results[i].args['value'])), decimals),
                abi
            },
            opts: {
                attempts: 10,
                backoff: {
                    type: 'exponential',
                    delay: 5000,
                },
                removeOnFail: 1000,
                removeOnComplete: 2000
            }
        });
    }
    if (arr.length > 0) {
        await checkLogs_erc20_usdt_Queue.addBulk(arr);
    }

    globalMonitor.endTimer(`queryFilter-${job.id}`);
    globalMonitor.recordJobProcessed();

}, {
    connection: connection10,
    concurrency: 1
});


worker.on("completed", (job, result) => {
    logger.queueCompleted(`${coin}-queryFilter`, job.id, result);
});

worker.on("failed", (job, err) => {
    globalMonitor.endTimer(`queryFilter-${job.id}`);
    globalMonitor.recordJobFailed();
    logger.queueFailed(`${coin}-queryFilter`, job.id, err, job.data);
});

worker.on("error", (err) => {
    logger.queueError(`${coin}-queryFilter`, err);
});

worker.on('paused', (job) => {
    logger.queuePaused(`${coin}-queryFilter`, job.id);
});

worker.on('resumed', (job) => {
    logger.queueResumed(`${coin}-queryFilter`, job.id);
});

worker.on('closed', () => {
    logger.queueClosed(`${coin}-queryFilter`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            logger.error(`Process ${type} event`, err, { origin });
            await worker.close();
            process.exit(0)
        } catch (closeError) {
            logger.error('Failed to close worker gracefully', closeError);
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        logger.info(`Received signal ${type}, shutting down gracefully`);
        try {
            await worker.close();
            logger.info('Worker closed successfully');
        } catch (error) {
            logger.error('Error closing worker', error);
        } finally {
            process.kill(process.pid, type)
        }
    })
});