const {
    ethers
} = require("ethers");

const moment = require('moment');

const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const connection10 = require('../config/redis_config')(10);

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const {
    address,
    abi,
    decimals
} = require("../abi/erc20-usdt");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);


const {
    coin
} = require('../config/coin_config');

const checkLogs_erc20_usdt_Queue = new QueuePro(`checkLogs-${coin}`, {
    connection: connection10
});

const contract = new ethers.Contract(address, abi, provider);

const worker = new WorkerPro('erc20-usdt-queryFilter', async (job) => {

    console.log(job.data);

    let starnum =  Number(job.data.dbBlockNum);

    const results = await contract.queryFilter('Transfer', starnum, starnum + 1);

    let arr = [];
    for (let i = 0; i < results.length; i++) {

        // console.log(ethers.utils.formatUnits(ethers.BigNumber.from((results[i].args['value'])), decimals), results[i].transactionHash);
        arr.push({
            name: `num:${results[i].transactionHash}`,
            data: {
                blockNumber: results[i].blockNumber,
                transactionHash: results[i].transactionHash,
                from: (results[i].args['from']).toLowerCase(),
                to: (results[i].args['to']).toLowerCase(),
                chainID: 'erc20',
                contractAddress: address,
                chain: 'erc20-usdt',
                decimals,
                value: ethers.utils.formatUnits(ethers.BigNumber.from((results[i].args['value'])), decimals),
                abi
            },
            opts: {
                attempts: 10,
                backoff: {
                    type: 'exponential',
                    delay: 5000,
                },
                removeOnFail: 1000,
                removeOnComplete: 2000
            }
        });
    }
    if (arr.length > 0) {
        await checkLogs_erc20_usdt_Queue.addBulk(arr);
    }

}, {
    connection: connection10,
    concurrency: 1
});


worker.on("completed", (job, result) => {
    // console.log(`Completed job on queue bep20-usdt-queryFilter`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue bep20-usdt-queryFilter`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'), job.data);
});

worker.on("error", (err) => {
    console.log(`error job on queue bep20-usdt-queryFilter`, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('paused', (job) => {
    console.error(`paused job on queue bep20-usdt-queryFilter`, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('resumed', (job) => {
    console.error(`resumed job on queue bep20-usdt-queryFilter`, job, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on('closed', () => {
    console.error(`worker bep20-usdt-queryFilter 已退出`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            process.exit(0)
        } catch (_) {
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});