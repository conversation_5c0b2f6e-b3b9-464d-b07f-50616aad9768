const {
    ethers
} = require("ethers");

const moment = require('moment');

const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const connection10 = require('../config/redis_config')(10);

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);

const Redis = require("ioredis");
const redis = new Redis({
    ...connection10
});

const {
    coin
} = require('../config/coin_config');

const queryFilter_Queue = new QueuePro(`${coin}-queryFilter`, {
    connection: connection10
});

const worker = new WorkerPro(`${coin}-block-track`, async (job) => {

    // console.log(1111);
    

    let num = await provider.getBlockNumber();

    // console.log('num',num);
    

    let last_db_num = await redis.get(`${coin}-last-db-num`);

    if (!last_db_num) {
        await redis.set(`${coin}-last-db-num`, num);
        last_db_num = num;
    } else {
        // 确保 last_db_num 是数字类型
        last_db_num = parseInt(last_db_num, 10);
    }

    if (last_db_num > num - 15) {

        let msg = `太快:${num-last_db_num}`;

        await job.update({
            dbBlockNum: last_db_num,
            chainBlockNumber: num,
            res: msg
        });

        // console.log(msg);

        return;
    }


    let msg_nm = `正常，相差:${num-last_db_num}`;

    await job.update({
        dbBlockNum: last_db_num,
        chainBlockNumber: num,
        res: msg_nm
    });

    // console.log(msg_nm);

    await queryFilter_Queue.add(`num_${last_db_num}`, {
        dbBlockNum: last_db_num,
        chainBlockNumber: num
    }, {
        attempts: 10,
        backoff: {
            type: 'exponential',
            delay: 5000,
        },
        removeOnComplete: 1000,
        removeOnFail: 1000
    });

    try {
        await redis.incrby(`${coin}-last-db-num`,2);
    } catch (error) {
        console.error(error);
        console.error('这里戳。。。。。。。。。。。。');
    }

}, {
    connection: connection10
});


worker.on("completed", (job, result) => {
    // console.log(`Completed job on queue trc20_block_track`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("error", (err) => {
    console.log(`error job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});


worker.on('closed', () => {
    console.error(`worker ${coin} 已退出`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            process.exit(0)
        } catch (_) {
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});