const {
    ethers
} = require("ethers");

const moment = require('moment');
const { createLogger } = require('../utils/logger');

const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const connection10 = require('../config/redis_config')(10);

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);

const Redis = require("ioredis");
const redis = new Redis({
    ...connection10
});

const {
    coin
} = require('../config/coin_config');

const queryFilter_Queue = new QueuePro(`${coin}-queryFilter`, {
    connection: connection10
});

const logger = createLogger('incrNum');

const worker = new WorkerPro(`${coin}-block-track`, async (job) => {

    logger.debug('Starting block tracking job');


    let num = await provider.getBlockNumber();

    logger.debug('Current block number', { blockNumber: num });
    

    let last_db_num = await redis.get(`${coin}-last-db-num`);

    if (!last_db_num) {
        await redis.set(`${coin}-last-db-num`, num);
        last_db_num = num;
    } else {
        // 确保 last_db_num 是数字类型
        last_db_num = parseInt(last_db_num, 10);
    }

    if (last_db_num > num - 15) {

        let msg = `Block processing too fast: ${num-last_db_num} blocks behind`;

        await job.update({
            dbBlockNum: last_db_num,
            chainBlockNumber: num,
            res: msg
        });

        logger.info(msg, { dbBlockNum: last_db_num, chainBlockNumber: num });

        return;
    }


    let msg_nm = `Normal processing, ${num-last_db_num} blocks behind`;

    await job.update({
        dbBlockNum: last_db_num,
        chainBlockNumber: num,
        res: msg_nm
    });

    logger.blockProcessing(last_db_num, num);

    await queryFilter_Queue.add(`num_${last_db_num}`, {
        dbBlockNum: last_db_num,
        chainBlockNumber: num
    }, {
        attempts: 10,
        backoff: {
            type: 'exponential',
            delay: 5000,
        },
        removeOnComplete: 1000,
        removeOnFail: 1000
    });

    try {
        await redis.incrby(`${coin}-last-db-num`,2);
        logger.debug('Successfully incremented block number');
    } catch (error) {
        logger.error('Failed to increment block number in Redis', error);
    }

}, {
    connection: connection10
});


worker.on("completed", (job, result) => {
    logger.queueCompleted(`${coin}-block-track`, job.id, result);
});

worker.on("failed", (job, err) => {
    logger.queueFailed(`${coin}-block-track`, job.id, err, job.data);
});

worker.on("error", (err) => {
    logger.queueError(`${coin}-block-track`, err);
});


worker.on('closed', () => {
    logger.queueClosed(`${coin}-block-track`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            logger.error(`Process ${type} event`, err, { origin });
            await worker.close();
            process.exit(0)
        } catch (closeError) {
            logger.error('Failed to close worker gracefully', closeError);
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        logger.info(`Received signal ${type}, shutting down gracefully`);
        try {
            await worker.close();
            logger.info('Worker closed successfully');
        } catch (error) {
            logger.error('Error closing worker', error);
        } finally {
            process.kill(process.pid, type)
        }
    })
});