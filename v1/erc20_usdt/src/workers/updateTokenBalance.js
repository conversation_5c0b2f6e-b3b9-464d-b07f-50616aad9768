const connection10 = require('../config/redis_config')(10);

const moment = require('moment');
const { createLogger } = require('../utils/logger');

const {
    QueuePro,
    WorkerPro,
    QueueSchedulerPro
} = require('@taskforcesh/bullmq-pro');


const {
    ethers, constants
} = require("ethers");

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);
const logger = createLogger('updateTokenBalance');

const QueueName = "updateerc20UsdtBalanceQueuePro";

const mysqlConfig = require('../config/mysqlConfig');

const knex = require('knex')({
    client: 'mysql2',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    },
    pool: {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 30000,
        createTimeoutMillis: 30000,
        destroyTimeoutMillis: 5000,
        idleTimeoutMillis: 30000,
        reapIntervalMillis: 1000,
        createRetryIntervalMillis: 100,
        propagateCreateError: false
    },
    acquireConnectionTimeout: 30000,
    debug: process.env.NODE_ENV === 'development'
});

const worker = new WorkerPro(QueueName, async (job) => {

    logger.debug('Processing balance update job', job.data);

    let address = job.data.address;
    let table = job.data.table;
    let field = job.data.field;
    const contractAddress = job.data.contractAddress;
    const abi = job.data.abi;
    const decimals = job.data.decimals;

    let erc20 = new ethers.Contract(contractAddress, abi, provider);

    let balanceCoin = await erc20.balanceOf(address);
    let balance = ethers.utils.formatUnits(String(balanceCoin), decimals);
    if (balance) {
        logger.debug('Retrieved balance', { address, balance, table });

        if(table == 'w_erc20_address'){
            let resss = await knex(table).update({
                [field]: balance,
                ck_conletion:0
            }).where({
                address: address
            });
    
            logger.balanceUpdated(address, balance, table);
            return resss;
        }else{
            let resss = await knex(table).update({
                [field]: balance,
            }).where({
                address: address
            });

            logger.balanceUpdated(address, balance, table);
            return resss;
        }
        
    }
}, {
    connection: connection10,
    concurrency: 1
});


worker.on("completed", (job, result) => {
    logger.queueCompleted(QueueName, job.id, result);
});

worker.on("failed", (job, err) => {
    logger.queueFailed(QueueName, job.id, err, job.data);
});

worker.on("error", (err) => {
    logger.queueError(QueueName, err);
});

worker.on('paused', (job) => {
    logger.queuePaused(QueueName, job.id);
});

worker.on('resumed', (job) => {
    logger.queueResumed(QueueName, job.id);
});

worker.on('closed', () => {
    logger.queueClosed(QueueName);
});

const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            logger.error(`Process ${type} event`, err, { origin });
            await worker.close();
            process.exit(0)
        } catch (closeError) {
            logger.error('Failed to close worker gracefully', closeError);
            process.exit(1)
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        logger.info(`Received signal ${type}, shutting down gracefully`);
        try {
            await worker.close();
            logger.info('Worker closed successfully');
        } catch (error) {
            logger.error('Error closing worker', error);
        } finally {
            process.kill(process.pid, type)
        }
    })
});