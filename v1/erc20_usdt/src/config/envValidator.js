/**
 * 环境变量验证器
 * 验证所有必需的环境变量是否存在
 */

const requiredEnvVars = [
    'mysql_host',
    'mysql_user', 
    'mysql_port',
    'mysql_password',
    'mysql_database',
    'redis_host',
    'redis_port',
    'ui_username',
    'ui_password',
    'ui_session_secret'
];

const optionalEnvVars = [
    'redis_password'
];

function validateEnvironment() {
    const missingVars = [];
    const warnings = [];

    // 检查必需的环境变量
    requiredEnvVars.forEach(varName => {
        if (!process.env[varName]) {
            missingVars.push(varName);
        }
    });

    // 检查可选的环境变量
    optionalEnvVars.forEach(varName => {
        if (!process.env[varName]) {
            warnings.push(`Optional environment variable ${varName} is not set`);
        }
    });

    // 如果有缺失的必需变量，抛出错误
    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:');
        missingVars.forEach(varName => {
            console.error(`   - ${varName}`);
        });
        console.error('\nPlease check your .env file and ensure all required variables are set.');
        process.exit(1);
    }

    // 输出警告信息
    if (warnings.length > 0) {
        console.warn('⚠️  Environment warnings:');
        warnings.forEach(warning => {
            console.warn(`   - ${warning}`);
        });
    }

    console.log('✅ Environment variables validation passed');
    
    // 返回配置对象
    return {
        mysql: {
            host: process.env.mysql_host,
            user: process.env.mysql_user,
            port: parseInt(process.env.mysql_port, 10),
            password: process.env.mysql_password,
            database: process.env.mysql_database
        },
        redis: {
            host: process.env.redis_host,
            port: parseInt(process.env.redis_port, 10),
            password: process.env.redis_password || ''
        },
        ui: {
            username: process.env.ui_username,
            password: process.env.ui_password,
            sessionSecret: process.env.ui_session_secret
        }
    };
}

module.exports = {
    validateEnvironment,
    requiredEnvVars,
    optionalEnvVars
};
