const { createLogger } = require('../utils/logger');
const logger = createLogger('redis_config');

module.exports = (db) => {
    const config = {
        port: parseInt(process.env.redis_port, 10) || 6379,
        host: process.env.redis_host || '127.0.0.1',
        db: db || 0,
        password: process.env.redis_password || '',

        // 连接池配置
        maxRetriesPerRequest: 3,
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxLoadingTimeout: 5000,

        // 重连配置
        connectTimeout: 10000,
        commandTimeout: 5000,
        lazyConnect: true,
        keepAlive: 30000,

        // 重试策略
        retryStrategy: (times) => {
            const delay = Math.min(times * 50, 2000);
            logger.warn(`Redis connection retry attempt ${times}, delay: ${delay}ms`);
            return delay;
        },

        // 事件处理
        onConnect: () => {
            logger.info(`Redis connected to ${config.host}:${config.port}, db: ${db}`);
        },

        onReady: () => {
            logger.info(`Redis ready on db: ${db}`);
        },

        onError: (error) => {
            logger.error(`Redis connection error on db: ${db}`, error);
        },

        onClose: () => {
            logger.warn(`Redis connection closed on db: ${db}`);
        },

        onReconnecting: () => {
            logger.info(`Redis reconnecting on db: ${db}`);
        }
    };

    // 如果没有密码，删除password字段
    if (!config.password) {
        delete config.password;
    }

    return config;
}