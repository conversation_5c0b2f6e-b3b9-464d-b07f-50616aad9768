/**
 * 配置文件验证器
 * 验证ABI配置、合约地址和其他关键配置的有效性
 */

const { ethers } = require('ethers');
const { createLogger } = require('../utils/logger');

const logger = createLogger('configValidator');

/**
 * 验证以太坊地址格式
 */
function isValidEthereumAddress(address) {
    try {
        return ethers.utils.isAddress(address);
    } catch (error) {
        return false;
    }
}

/**
 * 验证ABI格式
 */
function isValidABI(abi) {
    try {
        if (!Array.isArray(abi)) {
            return false;
        }
        
        // 检查是否包含必需的ERC20函数
        const requiredFunctions = ['balanceOf', 'transfer', 'transferFrom'];
        const requiredEvents = ['Transfer'];
        
        const functions = abi.filter(item => item.type === 'function').map(item => item.name);
        const events = abi.filter(item => item.type === 'event').map(item => item.name);
        
        const hasFunctions = requiredFunctions.every(func => functions.includes(func));
        const hasEvents = requiredEvents.every(event => events.includes(event));
        
        return hasFunctions && hasEvents;
    } catch (error) {
        return false;
    }
}

/**
 * 验证RPC URL
 */
async function isValidRpcUrl(rpcUrl) {
    try {
        const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
        const network = await provider.getNetwork();
        return network && network.chainId > 0;
    } catch (error) {
        logger.warn('RPC URL validation failed', { rpcUrl, error: error.message });
        return false;
    }
}

/**
 * 验证合约是否存在于指定地址
 */
async function isValidContract(contractAddress, rpcUrl) {
    try {
        const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
        const code = await provider.getCode(contractAddress);
        return code !== '0x';
    } catch (error) {
        logger.warn('Contract validation failed', { contractAddress, error: error.message });
        return false;
    }
}

/**
 * 验证ERC20配置
 */
async function validateERC20Config() {
    const errors = [];
    const warnings = [];
    
    try {
        // 导入配置
        const { address, abi, decimals } = require('../abi/erc20-usdt');
        const { rpcUrl } = require('./rpcUrl_config');
        const { coin } = require('./coin_config');
        
        logger.info('Starting ERC20 configuration validation');
        
        // 验证合约地址
        if (!address) {
            errors.push('Contract address is missing');
        } else if (!isValidEthereumAddress(address)) {
            errors.push(`Invalid contract address format: ${address}`);
        }
        
        // 验证ABI
        if (!abi) {
            errors.push('ABI is missing');
        } else if (!isValidABI(abi)) {
            errors.push('Invalid ABI format or missing required ERC20 functions/events');
        }
        
        // 验证decimals
        if (typeof decimals !== 'number' || decimals < 0 || decimals > 18) {
            errors.push(`Invalid decimals value: ${decimals}. Must be a number between 0 and 18`);
        }
        
        // 验证coin配置
        if (!coin || typeof coin !== 'string') {
            errors.push('Invalid coin configuration');
        }
        
        // 验证RPC URL
        if (!rpcUrl) {
            errors.push('RPC URL is missing');
        } else {
            const isRpcValid = await isValidRpcUrl(rpcUrl);
            if (!isRpcValid) {
                warnings.push(`RPC URL may be invalid or unreachable: ${rpcUrl}`);
            }
        }
        
        // 验证合约是否存在（如果地址和RPC都有效）
        if (address && isValidEthereumAddress(address) && rpcUrl) {
            const isContractValid = await isValidContract(address, rpcUrl);
            if (!isContractValid) {
                warnings.push(`No contract found at address: ${address}`);
            }
        }
        
        // 输出结果
        if (errors.length > 0) {
            logger.error('❌ Configuration validation failed');
            errors.forEach(error => {
                logger.error(`   - ${error}`);
            });
            throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
        }
        
        if (warnings.length > 0) {
            logger.warn('⚠️  Configuration warnings:');
            warnings.forEach(warning => {
                logger.warn(`   - ${warning}`);
            });
        }
        
        logger.success('✅ ERC20 configuration validation passed');
        
        return {
            valid: true,
            config: {
                address,
                abi,
                decimals,
                rpcUrl,
                coin
            },
            warnings
        };
        
    } catch (error) {
        logger.error('Configuration validation error', error);
        throw error;
    }
}

/**
 * 验证数据库表结构（基本检查）
 */
function validateDatabaseConfig() {
    const requiredTables = [
        'address_all',
        'w_erc20_address'
    ];
    
    const requiredFields = {
        'address_all': ['address', 'erc20-usdt'],
        'w_erc20_address': ['address', 'usdt', 'ck_conletion']
    };
    
    logger.info('Database configuration validation completed');
    
    return {
        requiredTables,
        requiredFields
    };
}

module.exports = {
    validateERC20Config,
    validateDatabaseConfig,
    isValidEthereumAddress,
    isValidABI,
    isValidRpcUrl,
    isValidContract
};
