#!/bin/bash

# ERC20 USDT Monitor 停止脚本
# 优雅地停止所有服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log_info "Stopping ERC20 USDT Monitor..."
log_info "Project directory: $PROJECT_DIR"

# 切换到项目目录
cd "$PROJECT_DIR"

# 查找并停止相关进程
stop_processes() {
    log_info "Looking for running processes..."
    
    # 查找相关的Node.js进程
    PIDS=$(pgrep -f "node.*src.*" 2>/dev/null || true)
    
    if [ -z "$PIDS" ]; then
        log_info "No running processes found"
        return 0
    fi
    
    log_info "Found running processes:"
    ps -p $PIDS -o pid,ppid,cmd 2>/dev/null || true
    
    # 优雅停止进程
    log_info "Sending SIGTERM to processes..."
    for pid in $PIDS; do
        if kill -TERM "$pid" 2>/dev/null; then
            log_info "Sent SIGTERM to process $pid"
        fi
    done
    
    # 等待进程停止
    log_info "Waiting for processes to stop gracefully..."
    sleep 5
    
    # 检查是否还有进程在运行
    REMAINING_PIDS=$(pgrep -f "node.*src.*" 2>/dev/null || true)
    
    if [ -n "$REMAINING_PIDS" ]; then
        log_warning "Some processes are still running, sending SIGKILL..."
        for pid in $REMAINING_PIDS; do
            if kill -KILL "$pid" 2>/dev/null; then
                log_warning "Killed process $pid"
            fi
        done
        sleep 2
    fi
    
    # 最终检查
    FINAL_PIDS=$(pgrep -f "node.*src.*" 2>/dev/null || true)
    if [ -z "$FINAL_PIDS" ]; then
        log_success "All processes stopped successfully ✓"
    else
        log_error "Some processes could not be stopped:"
        ps -p $FINAL_PIDS -o pid,ppid,cmd 2>/dev/null || true
        return 1
    fi
}

# 停止PM2进程（如果使用PM2）
stop_pm2_processes() {
    if command -v pm2 &> /dev/null; then
        log_info "Checking for PM2 processes..."
        
        # 检查是否有相关的PM2进程
        if pm2 list | grep -q "erc20-usdt\|v1/erc20-usdt"; then
            log_info "Stopping PM2 processes..."
            pm2 stop ecosystem.config.js 2>/dev/null || true
            pm2 delete ecosystem.config.js 2>/dev/null || true
            log_success "PM2 processes stopped ✓"
        else
            log_info "No PM2 processes found"
        fi
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log_info "Cleaning up temporary files..."
    
    # 清理可能的PID文件
    if [ -f "pids" ]; then
        rm -f pids
        log_info "Removed PID files"
    fi
    
    # 清理日志锁文件
    if [ -d "logs" ]; then
        find logs -name "*.lock" -delete 2>/dev/null || true
    fi
    
    log_success "Cleanup completed ✓"
}

# 显示停止后的状态
show_status() {
    log_info "Checking final status..."
    
    # 检查端口占用
    if command -v lsof &> /dev/null; then
        PORT_8118=$(lsof -ti:8118 2>/dev/null || true)
        if [ -n "$PORT_8118" ]; then
            log_warning "Port 8118 is still in use by process: $PORT_8118"
        else
            log_success "Port 8118 is free ✓"
        fi
    fi
    
    # 检查Redis连接数（如果可能）
    if [ -f ".env" ]; then
        source .env
        REDIS_HOST=${redis_host:-127.0.0.1}
        REDIS_PORT=${redis_port:-6379}
        
        if command -v redis-cli &> /dev/null; then
            REDIS_CLIENTS=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" info clients 2>/dev/null | grep connected_clients | cut -d: -f2 | tr -d '\r' || echo "unknown")
            log_info "Redis connected clients: $REDIS_CLIENTS"
        fi
    fi
}

# 主函数
main() {
    echo "=================================="
    echo "  ERC20 USDT Monitor Shutdown"
    echo "=================================="
    echo
    
    stop_pm2_processes
    stop_processes
    cleanup_temp_files
    show_status
    
    echo
    log_success "🛑 ERC20 USDT Monitor stopped successfully!"
    echo
    log_info "To start the services again, run: ./scripts/start.sh"
    echo
}

# 处理中断信号
trap 'log_warning "Script interrupted"; exit 1' INT TERM

# 运行主函数
main "$@"
