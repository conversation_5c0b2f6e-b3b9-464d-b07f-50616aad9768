#!/bin/bash

# ERC20 USDT Monitor 启动脚本
# 包含环境检查和服务启动

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log_info "Starting ERC20 USDT Monitor..."
log_info "Project directory: $PROJECT_DIR"

# 切换到项目目录
cd "$PROJECT_DIR"

# 检查Node.js版本
check_node_version() {
    log_info "Checking Node.js version..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
    
    if [ "$MAJOR_VERSION" -lt 18 ]; then
        log_error "Node.js version $NODE_VERSION is not supported. Please use Node.js 18 or higher."
        exit 1
    fi
    
    log_success "Node.js version: $NODE_VERSION ✓"
}

# 检查环境文件
check_env_file() {
    log_info "Checking environment file..."
    
    if [ ! -f ".env" ]; then
        log_error ".env file not found"
        log_info "Please create .env file with required configuration"
        exit 1
    fi
    
    log_success "Environment file found ✓"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    if [ ! -d "node_modules" ]; then
        log_warning "node_modules not found, installing dependencies..."
        npm install
    fi
    
    log_success "Dependencies checked ✓"
}

# 检查Redis连接
check_redis() {
    log_info "Checking Redis connection..."
    
    # 从.env文件读取Redis配置
    if [ -f ".env" ]; then
        source .env
        REDIS_HOST=${redis_host:-127.0.0.1}
        REDIS_PORT=${redis_port:-6379}
        
        # 使用nc检查Redis连接
        if command -v nc &> /dev/null; then
            if nc -z "$REDIS_HOST" "$REDIS_PORT" 2>/dev/null; then
                log_success "Redis connection: $REDIS_HOST:$REDIS_PORT ✓"
            else
                log_warning "Cannot connect to Redis at $REDIS_HOST:$REDIS_PORT"
                log_warning "Please ensure Redis is running"
            fi
        else
            log_warning "nc command not found, skipping Redis connection check"
        fi
    fi
}

# 检查MySQL连接
check_mysql() {
    log_info "Checking MySQL connection..."
    
    if [ -f ".env" ]; then
        source .env
        MYSQL_HOST=${mysql_host:-localhost}
        MYSQL_PORT=${mysql_port:-3306}
        
        # 使用nc检查MySQL连接
        if command -v nc &> /dev/null; then
            if nc -z "$MYSQL_HOST" "$MYSQL_PORT" 2>/dev/null; then
                log_success "MySQL connection: $MYSQL_HOST:$MYSQL_PORT ✓"
            else
                log_warning "Cannot connect to MySQL at $MYSQL_HOST:$MYSQL_PORT"
                log_warning "Please ensure MySQL is running"
            fi
        else
            log_warning "nc command not found, skipping MySQL connection check"
        fi
    fi
}

# 运行配置验证
run_config_validation() {
    log_info "Running configuration validation..."
    
    # 运行环境变量验证
    if node -r dotenv/config -e "require('./src/config/envValidator').validateEnvironment()" dotenv_config_path=./.env 2>/dev/null; then
        log_success "Configuration validation passed ✓"
    else
        log_error "Configuration validation failed"
        exit 1
    fi
}

# 创建日志目录
create_log_directory() {
    log_info "Creating log directory..."
    
    if [ ! -d "logs" ]; then
        mkdir -p logs
        log_success "Log directory created ✓"
    else
        log_success "Log directory exists ✓"
    fi
}

# 启动服务
start_services() {
    log_info "Starting services..."
    
    # 检查是否已经在运行
    if pgrep -f "node.*src.*" > /dev/null; then
        log_warning "Some Node.js processes are already running"
        log_info "Use './scripts/stop.sh' to stop existing processes first"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # 启动所有服务
    log_info "Starting all services with npm start..."
    npm start &
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if pgrep -f "node.*src.*" > /dev/null; then
        log_success "Services started successfully ✓"
        log_info "Dashboard available at: http://localhost:8118/ui"
        log_info "Health check available at: http://localhost:8118/health"
        log_info "Status check available at: http://localhost:8118/status"
    else
        log_error "Failed to start services"
        exit 1
    fi
}

# 主函数
main() {
    echo "=================================="
    echo "  ERC20 USDT Monitor Startup"
    echo "=================================="
    echo
    
    check_node_version
    check_env_file
    check_dependencies
    check_redis
    check_mysql
    run_config_validation
    create_log_directory
    start_services
    
    echo
    log_success "🚀 ERC20 USDT Monitor started successfully!"
    echo
    log_info "To stop the services, run: ./scripts/stop.sh"
    log_info "To view logs, run: ./scripts/logs.sh"
    echo
}

# 运行主函数
main "$@"
