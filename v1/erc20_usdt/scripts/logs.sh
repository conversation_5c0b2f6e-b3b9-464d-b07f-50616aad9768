#!/bin/bash

# ERC20 USDT Monitor 日志查看脚本
# 提供多种日志查看选项

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 切换到项目目录
cd "$PROJECT_DIR"

# 显示帮助信息
show_help() {
    echo "ERC20 USDT Monitor - Log Viewer"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -f, --follow        Follow log output (like tail -f)"
    echo "  -n, --lines NUM     Show last NUM lines (default: 50)"
    echo "  -w, --worker NAME   Show logs for specific worker"
    echo "  -e, --errors        Show only error logs"
    echo "  -l, --list          List available log files"
    echo "  --clear             Clear all log files"
    echo
    echo "Workers:"
    echo "  getLogs, incrNum, updateTokenBalance, checkLogs, ui"
    echo
    echo "Examples:"
    echo "  $0                  # Show recent logs from all workers"
    echo "  $0 -f               # Follow all logs in real-time"
    echo "  $0 -w getLogs       # Show logs from getLogs worker"
    echo "  $0 -e               # Show only error logs"
    echo "  $0 -n 100           # Show last 100 lines"
}

# 列出可用的日志文件
list_log_files() {
    log_info "Available log files:"
    
    if [ -d "logs" ]; then
        find logs -name "*.log" -type f | sort | while read -r logfile; do
            size=$(du -h "$logfile" | cut -f1)
            modified=$(stat -c %y "$logfile" 2>/dev/null | cut -d' ' -f1,2 | cut -d'.' -f1 || stat -f %Sm "$logfile" 2>/dev/null)
            echo "  📄 $logfile ($size, modified: $modified)"
        done
    else
        log_warning "No logs directory found"
    fi
    
    # 检查是否有运行中的进程
    if pgrep -f "node.*src.*" > /dev/null; then
        echo
        log_info "Running processes:"
        ps -f -p $(pgrep -f "node.*src.*" | tr '\n' ',' | sed 's/,$//')
    fi
}

# 清理日志文件
clear_logs() {
    log_warning "This will delete all log files. Are you sure?"
    read -p "Type 'yes' to confirm: " -r
    
    if [ "$REPLY" = "yes" ]; then
        if [ -d "logs" ]; then
            rm -f logs/*.log
            log_success "All log files cleared"
        else
            log_info "No logs directory found"
        fi
    else
        log_info "Operation cancelled"
    fi
}

# 显示特定worker的日志
show_worker_logs() {
    local worker=$1
    local lines=${2:-50}
    local follow=${3:-false}
    
    local log_pattern="logs/*${worker}*.log"
    local log_files=$(ls $log_pattern 2>/dev/null || true)
    
    if [ -z "$log_files" ]; then
        log_warning "No log files found for worker: $worker"
        return 1
    fi
    
    log_info "Showing logs for worker: $worker"
    
    if [ "$follow" = true ]; then
        tail -f $log_files
    else
        tail -n "$lines" $log_files
    fi
}

# 显示错误日志
show_error_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    if [ ! -d "logs" ]; then
        log_warning "No logs directory found"
        return 1
    fi
    
    log_info "Showing error logs (last $lines lines):"
    
    if [ "$follow" = true ]; then
        tail -f logs/*.log | grep -i "error\|failed\|exception" --color=always
    else
        tail -n "$lines" logs/*.log | grep -i "error\|failed\|exception" --color=always || log_info "No errors found in recent logs"
    fi
}

# 显示所有日志
show_all_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    if [ ! -d "logs" ]; then
        log_warning "No logs directory found"
        log_info "Logs might be output to console. Check running processes:"
        if pgrep -f "node.*src.*" > /dev/null; then
            ps -f -p $(pgrep -f "node.*src.*" | tr '\n' ',' | sed 's/,$//')
        else
            log_info "No running processes found"
        fi
        return 1
    fi
    
    log_info "Showing all logs (last $lines lines per file):"
    
    if [ "$follow" = true ]; then
        tail -f logs/*.log
    else
        for logfile in logs/*.log; do
            if [ -f "$logfile" ]; then
                echo
                echo "=== $(basename "$logfile") ==="
                tail -n "$lines" "$logfile"
            fi
        done
    fi
}

# 主函数
main() {
    local follow=false
    local lines=50
    local worker=""
    local errors_only=false
    local list_files=false
    local clear_logs_flag=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--follow)
                follow=true
                shift
                ;;
            -n|--lines)
                lines="$2"
                shift 2
                ;;
            -w|--worker)
                worker="$2"
                shift 2
                ;;
            -e|--errors)
                errors_only=true
                shift
                ;;
            -l|--list)
                list_files=true
                shift
                ;;
            --clear)
                clear_logs_flag=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "=================================="
    echo "  ERC20 USDT Monitor - Logs"
    echo "=================================="
    echo
    
    # 执行相应的操作
    if [ "$clear_logs_flag" = true ]; then
        clear_logs
    elif [ "$list_files" = true ]; then
        list_log_files
    elif [ -n "$worker" ]; then
        show_worker_logs "$worker" "$lines" "$follow"
    elif [ "$errors_only" = true ]; then
        show_error_logs "$lines" "$follow"
    else
        show_all_logs "$lines" "$follow"
    fi
}

# 处理中断信号
trap 'echo; log_info "Log viewing stopped"; exit 0' INT TERM

# 运行主函数
main "$@"
